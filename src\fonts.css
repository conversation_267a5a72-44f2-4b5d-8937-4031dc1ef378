/* SF Pro Display Font Family */
@font-face {
  font-family: 'SF Pro Display';
  src: url('./assets/fonts/sf-pro/SFProDisplay-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('./assets/fonts/sf-pro/SFProDisplay-RegularItalic.woff2') format('woff2');
  font-weight: 400;
  font-style: italic;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('./assets/fonts/sf-pro/SFProDisplay-Light.woff2') format('woff2');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('./assets/fonts/sf-pro/SFProDisplay-LightItalic.woff2') format('woff2');
  font-weight: 300;
  font-style: italic;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('./assets/fonts/sf-pro/SFProDisplay-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('./assets/fonts/sf-pro/SFProDisplay-MediumItalic.woff2') format('woff2');
  font-weight: 500;
  font-style: italic;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('./assets/fonts/sf-pro/SFProDisplay-Semibold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('./assets/fonts/sf-pro/SFProDisplay-SemiboldItalic.woff2') format('woff2');
  font-weight: 600;
  font-style: italic;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('./assets/fonts/sf-pro/SFProDisplay-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('./assets/fonts/sf-pro/SFProDisplay-BoldItalic.woff2') format('woff2');
  font-weight: 700;
  font-style: italic;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('./assets/fonts/sf-pro/SFProDisplay-Heavy.woff2') format('woff2');
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('./assets/fonts/sf-pro/SFProDisplay-HeavyItalic.woff2') format('woff2');
  font-weight: 800;
  font-style: italic;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('./assets/fonts/sf-pro/SFProDisplay-Black.woff2') format('woff2');
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('./assets/fonts/sf-pro/SFProDisplay-BlackItalic.woff2') format('woff2');
  font-weight: 900;
  font-style: italic;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('./assets/fonts/sf-pro/SFProDisplay-Thin.woff2') format('woff2');
  font-weight: 100;
  font-style: normal;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('./assets/fonts/sf-pro/SFProDisplay-ThinItalic.woff2') format('woff2');
  font-weight: 100;
  font-style: italic;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('./assets/fonts/sf-pro/SFProDisplay-Ultralight.woff2') format('woff2');
  font-weight: 200;
  font-style: normal;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('./assets/fonts/sf-pro/SFProDisplay-UltralightItalic.woff2') format('woff2');
  font-weight: 200;
  font-style: italic;
}


/* Date Stamp Font */
@font-face {
  font-family: 'Date Stamp';
  src: url('./assets/fonts/date-stamp/date-stamp-regular.otf') format('truetype');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Date Stamp';
  src: url('./assets/fonts/date-stamp/date-stamp-bold.otf') format('truetype');
  font-weight: 400;
  font-style: normal;
}